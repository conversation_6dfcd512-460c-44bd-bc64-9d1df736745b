//+------------------------------------------------------------------+
//|                                          DatabaseLocationTest.mq5 |
//+------------------------------------------------------------------+
#include "../Include/MultiCurrency/Database.mqh"

void OnInit() {
    Print("Testing database location and tables...");
    
    // Try to connect to database
    if(DB::Connect("database911.sqlite")) {
        Print("✅ Database connection successful!");
        Print("Database file: ", DB::FileName(true));
        
        // Test a simple query to tasks table
        int request = DatabasePrepare(DB::Id(), "SELECT COUNT(*) FROM tasks;");
        if(request != INVALID_HANDLE) {
            struct Row {
                int count;
            } row;
            
            if(DatabaseReadBind(request, row)) {
                Print("✅ Tasks table found! Total tasks: ", row.count);
            } else {
                Print("❌ Could not read from tasks table");
            }
            DatabaseFinalize(request);
        } else {
            Print("❌ Could not query tasks table - table might not exist");
        }
        
        DB::Close();
    } else {
        Print("❌ Database connection failed!");
        Print("Looking for: ", DB::FileName(true));
    }
}

void OnTick() {
    // Do nothing
}

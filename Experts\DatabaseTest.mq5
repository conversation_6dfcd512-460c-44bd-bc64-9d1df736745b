//+------------------------------------------------------------------+
//|                                                DatabaseTest.mq5 |
//+------------------------------------------------------------------+
#include "../Include/MultiCurrency/Database.mqh"

void OnInit() {
    Print("Testing database connection...");
    
    // Try to connect to database
    if(DB::Connect("database911.sqlite")) {
        Print("✅ Database connection successful!");
        Print("Database file: ", DB::FileName(true));
        DB::Close();
    } else {
        Print("❌ Database connection failed!");
        Print("Looking for: ", DB::FileName(true));
    }
}

void OnTick() {
    // Do nothing
}

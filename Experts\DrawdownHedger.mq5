//+------------------------------------------------------------------+
//|                                             DrawdownHedger.mq5 |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023"
#property version   "1.03"
#property description "Automatic hedge trade executor based on drawdown"
#property strict

// Include Trade library
#include <Trade/Trade.mqh>

// We no longer use ATRtrailing.mqh, we'll implement custom trailing
// bool UseTrailingButton = false;  // We're removing this as we don't need it anymore

// Input parameters for trailing functionality
input group "Trailing Stop Settings"
input bool   UseTrailing = true;      // Use trailing stop for hedge positions
input int    TrailingTrigger = 700;   // Trigger trailing after X pips profit
input int    TrailingStop = 300;      // Stop loss distance in pips
input int    TrailingIncrement = 100; // Update trailing every X pips

// Manual Hedge Button settings
input group "Manual Hedge Button"
input bool   ShowManualHedgeButton = true;  // Show manual hedge button
input int    ManualButtonX = 10;           // X position for manual button
input int    ManualButtonY = 60;           // Y position for manual button
input color  ButtonColor = clrDarkOrange;  // Button color
input color  ButtonTextColor = clrWhite;   // Button text color

// Input parameters for position tracking (from LotsTracker)
input group "Position Tracking"
input int    FilterMagic = 0;           // Magic number filter (0 = show all positions)
input string FilterSymbol = "";         // Symbol filter (empty = current chart symbol)
input string AllSymbols = "ALL";        // Keyword to show all symbols

// Input parameters for hedge trading
input group "Hedge Settings"
input double LotThreshold = 0.5;        // Minimum lots to trigger hedging
input double DrawdownPercent = 2.0;     // Minimum drawdown percent to trigger hedging
input double LotMultiplier = 1.5;       // Multiplier for hedge position sizing
input int    HedgeMagic = 12345;        // Magic number for hedge trades
input double StopLoss = 50;             // Stop loss for hedge trades in points (0 = no SL)
input double TakeProfit = 100;          // Take profit for hedge trades in points (0 = no TP)
input bool   CloseHedgeOnRecovery = true; // Close hedge when original position recovers
input bool   ClosePositionsAfterHedge = true; // Close bad positions after hedge closes

// Display settings
input group "Display Settings"
input color  LongColor = clrLime;       // Long position color
input color  ShortColor = clrRed;       // Short position color
input color  HedgeColor = clrYellow;    // Hedge indicator color
input int    FontSize = 12;             // Font size
input ENUM_BASE_CORNER Corner = CORNER_RIGHT_UPPER; // Corner position
input int    XOffset = 250;              // X offset from corner
input int    YOffset = 20;              // Y offset from corner
input int    UpdateInterval = 1;        // Update interval in seconds
input string LabelPrefix = "Hedger";    // Label prefix
input bool   ShowBackground = true;     // Show background for better visibility

// Global variables
double totalLongLots = 0.0;
double totalShortLots = 0.0;
double longDrawdown = 0.0;
double shortDrawdown = 0.0;
bool hedgePlaced = false;
datetime lastHedgeTime = 0;
ulong hedgeTicket = 0;
string currentSymbol;
int lineSpacing = 20;
CTrade trade;

// Variables for hedge position tracking
double hedgeOpenPrice = 0.0;      // Opening price of hedge position 
string hedgeType = "";            // Type of hedge position (BUY/SELL)
double hedgeOpenProfit = 0.0;     // Profit at the time of hedge open - for tracking profit

// Variables for trailing stop logic
bool trailingActivated = false;   // Has trailing been activated for current position?
double lastTrailLevel = 0.0;      // Last level at which we updated the trailing stop
double trailStopPrice = 0.0;      // Current trailing stop price

// Manual hedge button
string ManualHedgeButtonName = "ManualHedgeButton";

// Structure to hold position information for sorting
struct PositionInfo
{
   ulong ticket;                // Position ticket
   double drawdownValue;        // Drawdown value in money
   double drawdownPercent;      // Drawdown percentage
   double volume;               // Position volume
   double profit;               // Current position profit
   double initialValue;         // Initial position value
   long magicNumber;            // Magic number
};

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // Set current symbol
   currentSymbol = Symbol();
   
   // Setup trade object
   trade.SetExpertMagicNumber(HedgeMagic);
   
   // Remove existing labels if any
   DeleteLabels();
   
   // Create text labels
   CreateLabel(LabelPrefix + "Long", "Long: 0.00 lots (DD: 0.00%)", LongColor, Corner, XOffset, YOffset);
   CreateLabel(LabelPrefix + "Short", "Short: 0.00 lots (DD: 0.00%)", ShortColor, Corner, XOffset, YOffset + lineSpacing);
   CreateLabel(LabelPrefix + "Hedge", "Hedge: None", HedgeColor, Corner, XOffset, YOffset + lineSpacing * 2);
   
   if(UseTrailing)
   {
      CreateLabel(LabelPrefix + "Trail", "Trailing: Not Active", HedgeColor, Corner, XOffset, YOffset + lineSpacing * 3);
   }
   
   // Create manual hedge button
   if(ShowManualHedgeButton)
   {
      CreateHedgeButton();
   }
   
   // Set timer for regular updates
   EventSetTimer(UpdateInterval);
   
   Print("DrawdownHedger: Initialization completed");
   
   // Initial calculation
   CalculatePositionsAndDrawdown();
   UpdateDisplay();
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // Clean up by removing all created objects and kill timer
   EventSetTimer(0);
   DeleteLabels();
   
   // Delete manual hedge button
   ObjectDelete(0, ManualHedgeButtonName);
   
   Print("DrawdownHedger: Deinitialized");
}

//+------------------------------------------------------------------+
//| Create manual hedge button                                       |
//+------------------------------------------------------------------+
void CreateHedgeButton()
{
   // Delete button if it exists
   ObjectDelete(0, ManualHedgeButtonName);
   
   // Create button
   if(!ObjectCreate(0, ManualHedgeButtonName, OBJ_BUTTON, 0, 0, 0))
   {
      Print("Error creating manual hedge button: ", GetLastError());
      return;
   }
   
   // Set button properties
   ObjectSetInteger(0, ManualHedgeButtonName, OBJPROP_XDISTANCE, ManualButtonX);
   ObjectSetInteger(0, ManualHedgeButtonName, OBJPROP_YDISTANCE, ManualButtonY);
   ObjectSetInteger(0, ManualHedgeButtonName, OBJPROP_XSIZE, 120);
   ObjectSetInteger(0, ManualHedgeButtonName, OBJPROP_YSIZE, 30);
   ObjectSetString(0, ManualHedgeButtonName, OBJPROP_TEXT, "Force Hedge");
   ObjectSetInteger(0, ManualHedgeButtonName, OBJPROP_COLOR, ButtonTextColor);
   ObjectSetInteger(0, ManualHedgeButtonName, OBJPROP_BGCOLOR, ButtonColor);
   ObjectSetInteger(0, ManualHedgeButtonName, OBJPROP_BORDER_COLOR, clrBlack);
   ObjectSetInteger(0, ManualHedgeButtonName, OBJPROP_FONTSIZE, 10);
   ObjectSetInteger(0, ManualHedgeButtonName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
   
   ChartRedraw();
}

//+------------------------------------------------------------------+
//| Chart event function (for button click)                          |
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
   // Check for button click
   if(id == CHARTEVENT_OBJECT_CLICK && sparam == ManualHedgeButtonName)
   {
      // Reset the button state (required for buttons to work correctly)
      ObjectSetInteger(0, ManualHedgeButtonName, OBJPROP_STATE, false);
      
      // Force a manual hedge
      if(!hedgePlaced)
      {
         ExecuteManualHedge();
      }
      else
      {
         Print("Hedge already in place - close existing hedge first");
      }
   }
}

//+------------------------------------------------------------------+
//| Execute a manual hedge based on current exposure                 |
//+------------------------------------------------------------------+
void ExecuteManualHedge()
{
   // Skip if already hedged
   if(hedgePlaced) return;
   
   // Update positions data
   CalculatePositionsAndDrawdown();
   
   // Determine which direction to hedge
   bool hedgeLong = (totalLongLots > totalShortLots);
   double volumeToHedge = 0;
   
   if(hedgeLong)
   {
      // More long exposure - place a SHORT hedge
      volumeToHedge = totalLongLots * LotMultiplier;
      
      // Normalize the volume according to the symbol's lot requirements
      volumeToHedge = NormalizeVolume(Symbol(), volumeToHedge);
      
      if(volumeToHedge < 0.01)
      {
         Print("Not enough volume to hedge (min 0.01 lots required)");
         return;
      }
      
      double sl = (StopLoss > 0) ? SymbolInfoDouble(Symbol(), SYMBOL_ASK) + StopLoss * _Point : 0;
      double tp = (TakeProfit > 0) ? SymbolInfoDouble(Symbol(), SYMBOL_ASK) - TakeProfit * _Point : 0;
      
      if(trade.Sell(volumeToHedge, Symbol(), 0, sl, tp, "Manual Hedge"))
      {
         hedgePlaced = true;
         hedgeTicket = trade.ResultOrder();
         hedgeType = "SELL";
         
         // Get opening price
         if(PositionSelectByTicket(hedgeTicket))
         {
            hedgeOpenPrice = PositionGetDouble(POSITION_PRICE_OPEN);
         }
         
         Print("Placed MANUAL SHORT hedge trade of ", volumeToHedge, " lots");
      }
      else
      {
         Print("Failed to place manual SHORT hedge trade. Error: ", GetLastError());
      }
   }
   else if(totalShortLots > 0)
   {
      // More short exposure - place a LONG hedge
      volumeToHedge = totalShortLots * LotMultiplier;
      
      // Normalize the volume according to the symbol's lot requirements
      volumeToHedge = NormalizeVolume(Symbol(), volumeToHedge);
      
      if(volumeToHedge < 0.01)
      {
         Print("Not enough volume to hedge (min 0.01 lots required)");
         return;
      }
      
      double sl = (StopLoss > 0) ? SymbolInfoDouble(Symbol(), SYMBOL_BID) - StopLoss * _Point : 0;
      double tp = (TakeProfit > 0) ? SymbolInfoDouble(Symbol(), SYMBOL_BID) + TakeProfit * _Point : 0;
      
      if(trade.Buy(volumeToHedge, Symbol(), 0, sl, tp, "Manual Hedge"))
      {
         hedgePlaced = true;
         hedgeTicket = trade.ResultOrder();
         hedgeType = "BUY";
         
         // Get opening price
         if(PositionSelectByTicket(hedgeTicket))
         {
            hedgeOpenPrice = PositionGetDouble(POSITION_PRICE_OPEN);
         }
         
         Print("Placed MANUAL LONG hedge trade of ", volumeToHedge, " lots");
      }
      else
      {
         Print("Failed to place manual LONG hedge trade. Error: ", GetLastError());
      }
   }
   else
   {
      Print("No positions to hedge");
   }
}

//+------------------------------------------------------------------+
//| Delete all labels created by this EA                             |
//+------------------------------------------------------------------+
void DeleteLabels()
{
   ObjectDelete(0, LabelPrefix + "Long");
   ObjectDelete(0, LabelPrefix + "Short");
   ObjectDelete(0, LabelPrefix + "Hedge");
   ObjectDelete(0, LabelPrefix + "Trail");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // We'll also update on ticks to ensure we're always displaying data
   static datetime lastUpdate = 0;
   datetime currentTime = TimeCurrent();
   static ulong lastHedgeTicket = 0;
   
   // Only update if enough time has passed (1 second)
   if(currentTime - lastUpdate >= 1)
   {
      // Check if hedge position was closed externally
      if(hedgePlaced && hedgeTicket != 0)
      {
         if(!PositionSelectByTicket(hedgeTicket))
         {
            // The hedge position was closed - get profit from history
            double hedgeProfit = GetHedgeProfit(hedgeTicket);
            Print("Hedge position ", hedgeTicket, " closed with profit: ", hedgeProfit);
            
            // Handle closing positions based on hedge profit
            if(ClosePositionsAfterHedge && hedgeProfit > 0)
            {
               ClosePositionsBasedOnHedgeProfit(hedgeProfit);
            }
            
            // Reset hedge tracking variables
            hedgePlaced = false;
            hedgeTicket = 0;
            hedgeType = "";
            hedgeOpenPrice = 0;
            trailingActivated = false;
            lastTrailLevel = 0.0;
         }
      }
      
      CalculatePositionsAndDrawdown();
      UpdateDisplay();
      CheckAndPlaceHedge();
      CheckHedgeManagement();
      
      // Check trailing stop for hedge position
      if(UseTrailing && hedgePlaced)
      {
         UpdateTrailingForHedge();
      }
      
      // Capture account balance after hedge placement for profit calculation
      if(hedgePlaced && hedgeTicket != 0 && hedgeTicket != lastHedgeTicket)
      {
         lastHedgeTicket = hedgeTicket;
         hedgeOpenProfit = AccountInfoDouble(ACCOUNT_PROFIT);
      }
      
      lastUpdate = currentTime;
   }
}

//+------------------------------------------------------------------+
//| Timer function                                                   |
//+------------------------------------------------------------------+
void OnTimer()
{
   // Calculate on timer event
   CalculatePositionsAndDrawdown();
   UpdateDisplay();
   CheckAndPlaceHedge();
   CheckHedgeManagement();
   
   // Check trailing stop for hedge position
   if(UseTrailing && hedgePlaced)
   {
      UpdateTrailingForHedge();
   }
}

//+------------------------------------------------------------------+
//| Calculate total long and short lots and drawdown                 |
//+------------------------------------------------------------------+
void CalculatePositionsAndDrawdown()
{
   totalLongLots = 0.0;
   totalShortLots = 0.0;
   longDrawdown = 0.0;
   shortDrawdown = 0.0;
   
   double longProfit = 0.0;
   double shortProfit = 0.0;
   double longInitialValue = 0.0;
   double shortInitialValue = 0.0;
   
   // Determine which symbol to use for filtering
   string symbolFilter = (FilterSymbol == "") ? currentSymbol : FilterSymbol;
   bool showAllSymbols = (symbolFilter == AllSymbols);
   
   // Loop through all open positions
   for(int i = 0; i < PositionsTotal(); i++)
   {
      ulong ticket = PositionGetTicket(i);
      if(ticket > 0)
      {
         // Get position properties
         string posSymbol = PositionGetString(POSITION_SYMBOL);
         long posMagic = PositionGetInteger(POSITION_MAGIC);
         long positionType = PositionGetInteger(POSITION_TYPE);
         double positionVolume = PositionGetDouble(POSITION_VOLUME);
         double positionProfit = PositionGetDouble(POSITION_PROFIT);
         double positionInitialPrice = PositionGetDouble(POSITION_PRICE_OPEN);
         double positionCurrentPrice = PositionGetDouble(POSITION_PRICE_CURRENT);
         double positionSwap = PositionGetDouble(POSITION_SWAP);
         double positionCommission = PositionGetDouble(POSITION_COMMISSION);
         double totalPnL = positionProfit + positionSwap + positionCommission;
         
         // Skip hedge positions when calculating
         if(posMagic == HedgeMagic)
         {
            // Update the hedge ticket if found
            if(!hedgePlaced)
            {
               hedgePlaced = true;
               hedgeTicket = ticket;
            }
            continue;
         }
         
         // Apply filters
         bool symbolMatch = (showAllSymbols || posSymbol == symbolFilter);
         bool magicMatch = (FilterMagic == 0 || posMagic == FilterMagic);
         
         // Add to totals if filters match
         if(symbolMatch && magicMatch)
         {
            if(positionType == POSITION_TYPE_BUY)
            {
               totalLongLots += positionVolume;
               longProfit += totalPnL;
               
               // Calculate position initial value
               double pointValue = SymbolInfoDouble(posSymbol, SYMBOL_TRADE_TICK_VALUE) / SymbolInfoDouble(posSymbol, SYMBOL_TRADE_TICK_SIZE);
               double contractSize = SymbolInfoDouble(posSymbol, SYMBOL_TRADE_CONTRACT_SIZE);
               double tickSize = SymbolInfoDouble(posSymbol, SYMBOL_TRADE_TICK_SIZE);
               
               double pips = (positionCurrentPrice - positionInitialPrice) / tickSize;
               double initialValue = positionVolume * contractSize * positionInitialPrice;
               longInitialValue += initialValue;
            }
            else if(positionType == POSITION_TYPE_SELL)
            {
               totalShortLots += positionVolume;
               shortProfit += totalPnL;
               
               // Calculate position initial value
               double pointValue = SymbolInfoDouble(posSymbol, SYMBOL_TRADE_TICK_VALUE) / SymbolInfoDouble(posSymbol, SYMBOL_TRADE_TICK_SIZE);
               double contractSize = SymbolInfoDouble(posSymbol, SYMBOL_TRADE_CONTRACT_SIZE);
               double tickSize = SymbolInfoDouble(posSymbol, SYMBOL_TRADE_TICK_SIZE);
               
               double pips = (positionInitialPrice - positionCurrentPrice) / tickSize;
               double initialValue = positionVolume * contractSize * positionInitialPrice;
               shortInitialValue += initialValue;
            }
         }
      }
   }
   
   // Calculate drawdown percentage for each direction
   if(longInitialValue > 0)
      longDrawdown = -longProfit / longInitialValue * 100.0;
   else
      longDrawdown = 0;
      
   if(shortInitialValue > 0)
      shortDrawdown = -shortProfit / shortInitialValue * 100.0;
   else
      shortDrawdown = 0;
   
   // Ensure drawdown is always positive (makes more sense for display)
   if(longDrawdown < 0) longDrawdown = 0;
   if(shortDrawdown < 0) shortDrawdown = 0;
}

//+------------------------------------------------------------------+
//| Update the on-screen display                                     |
//+------------------------------------------------------------------+
void UpdateDisplay()
{
   // Update labels
   ObjectSetString(0, LabelPrefix + "Long", OBJPROP_TEXT, "Long: " + DoubleToString(totalLongLots, 2) + " lots (DD: " + DoubleToString(longDrawdown, 2) + "%)");
   ObjectSetString(0, LabelPrefix + "Short", OBJPROP_TEXT, "Short: " + DoubleToString(totalShortLots, 2) + " lots (DD: " + DoubleToString(shortDrawdown, 2) + "%)");
   
   string hedgeInfo = "Hedge: None";
   if(hedgePlaced && PositionSelectByTicket(hedgeTicket))
   {
      double hedgeVolume = PositionGetDouble(POSITION_VOLUME);
      hedgeType = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? "BUY" : "SELL";
      hedgeInfo = "Hedge: " + hedgeType + " " + DoubleToString(hedgeVolume, 2) + " lots";
      
      // Add trailing info
      if(UseTrailing)
      {
         string trailInfo = "";
         double currentPrice = PositionGetDouble(POSITION_PRICE_CURRENT);
         double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
         double currentSL = PositionGetDouble(POSITION_SL);
         
         // Calculate profit pips
         double pips = 0;
         
         if(hedgeType == "BUY")
            pips = (currentPrice - openPrice) / _Point;
         else
            pips = (openPrice - currentPrice) / _Point;
            
         // Display trailing status
         if(trailingActivated)
         {
            trailInfo = "Trailing: Active +" + IntegerToString((int)pips) + " pips, SL=" + DoubleToString(currentSL, _Digits);
         }
         else
         {
            trailInfo = "Trailing: Waiting for +" + IntegerToString(TrailingTrigger) + " pips";
         }
         
         ObjectSetString(0, LabelPrefix + "Trail", OBJPROP_TEXT, trailInfo);
      }
   }
   else
   {
      hedgePlaced = false;
      hedgeType = "";
      
      if(UseTrailing)
      {
         ObjectSetString(0, LabelPrefix + "Trail", OBJPROP_TEXT, "Trailing: Not Active");
      }
   }
   
   ObjectSetString(0, LabelPrefix + "Hedge", OBJPROP_TEXT, hedgeInfo);
   
   // Force chart redraw
   ChartRedraw();
}

//+------------------------------------------------------------------+
//| Check conditions and place hedge if needed                       |
//+------------------------------------------------------------------+
void CheckAndPlaceHedge()
{
   // Skip if we already have a hedge position open
   if(hedgePlaced) return;
   
   // Minimum time between hedge attempts (5 seconds)
   if(TimeCurrent() - lastHedgeTime < 5) return;
   lastHedgeTime = TimeCurrent();
   
   // Determine which direction has the most drawdown
   bool longHasMostDrawdown = (longDrawdown > shortDrawdown) && (longDrawdown >= DrawdownPercent);
   bool shortHasMostDrawdown = (shortDrawdown > longDrawdown) && (shortDrawdown >= DrawdownPercent);
   
   // Long positions in drawdown - place a SHORT hedge
   if(longHasMostDrawdown && totalLongLots >= LotThreshold)
   {
      double hedgeVolume = totalLongLots * LotMultiplier;
      
      // Normalize the volume according to the symbol's lot requirements
      hedgeVolume = NormalizeVolume(Symbol(), hedgeVolume);
      
      double sl = (StopLoss > 0) ? SymbolInfoDouble(Symbol(), SYMBOL_ASK) + StopLoss * _Point : 0;
      double tp = (TakeProfit > 0) ? SymbolInfoDouble(Symbol(), SYMBOL_ASK) - TakeProfit * _Point : 0;
      
      if(trade.Sell(hedgeVolume, Symbol(), 0, sl, tp, "Drawdown Hedge"))
      {
         hedgePlaced = true;
         hedgeTicket = trade.ResultOrder();
         hedgeType = "SELL";
         
         // Get opening price
         if(PositionSelectByTicket(hedgeTicket))
         {
            hedgeOpenPrice = PositionGetDouble(POSITION_PRICE_OPEN);
         }
         
         Print("Placed SHORT hedge trade of ", hedgeVolume, " lots for long drawdown of ", longDrawdown, "%");
      }
      else
      {
         Print("Failed to place SHORT hedge trade. Error: ", GetLastError());
      }
   }
   // Short positions in drawdown - place a LONG hedge
   else if(shortHasMostDrawdown && totalShortLots >= LotThreshold)
   {
      double hedgeVolume = totalShortLots * LotMultiplier;
      
      // Normalize the volume according to the symbol's lot requirements
      hedgeVolume = NormalizeVolume(Symbol(), hedgeVolume);
      
      double sl = (StopLoss > 0) ? SymbolInfoDouble(Symbol(), SYMBOL_BID) - StopLoss * _Point : 0;
      double tp = (TakeProfit > 0) ? SymbolInfoDouble(Symbol(), SYMBOL_BID) + TakeProfit * _Point : 0;
      
      if(trade.Buy(hedgeVolume, Symbol(), 0, sl, tp, "Drawdown Hedge"))
      {
         hedgePlaced = true;
         hedgeTicket = trade.ResultOrder();
         hedgeType = "BUY";
         
         // Get opening price
         if(PositionSelectByTicket(hedgeTicket))
         {
            hedgeOpenPrice = PositionGetDouble(POSITION_PRICE_OPEN);
         }
         
         Print("Placed LONG hedge trade of ", hedgeVolume, " lots for short drawdown of ", shortDrawdown, "%");
      }
      else
      {
         Print("Failed to place LONG hedge trade. Error: ", GetLastError());
      }
   }
}

//+------------------------------------------------------------------+
//| Update trailing stop for hedge position                          |
//+------------------------------------------------------------------+
void UpdateTrailingForHedge()
{
   // Skip if no hedge is active or trailing not enabled
   if(!hedgePlaced || !UseTrailing) return;
   
   // Check if position exists
   if(!PositionSelectByTicket(hedgeTicket))
   {
      hedgePlaced = false;
      trailingActivated = false;
      lastTrailLevel = 0.0;
      return;
   }
   
   // Get position details
   double currentPrice = PositionGetDouble(POSITION_PRICE_CURRENT);
   double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
   double currentSL = PositionGetDouble(POSITION_SL);
   
   // Calculate profit in pips
   double profitPips = 0;
   if(hedgeType == "BUY")
      profitPips = (currentPrice - openPrice) / _Point;
   else
      profitPips = (openPrice - currentPrice) / _Point;
   
   // Check if we need to activate trailing
   if(!trailingActivated && profitPips >= TrailingTrigger)
   {
      // Activate trailing
      trailingActivated = true;
      
      // Calculate initial stop loss level
      if(hedgeType == "BUY")
      {
         // For BUY positions, stop is below current price
         trailStopPrice = currentPrice - (TrailingStop * _Point);
         lastTrailLevel = currentPrice;
      }
      else
      {
         // For SELL positions, stop is above current price
         trailStopPrice = currentPrice + (TrailingStop * _Point);
         lastTrailLevel = currentPrice;
      }
      
      // Set the initial trailing stop
      trade.PositionModify(hedgeTicket, trailStopPrice, 0);
      Print("Activated trailing stop for ", hedgeType, " position. Profit: ", profitPips, " pips, SL: ", trailStopPrice);
      return;
   }
   
   // If trailing is already active, check if we need to update SL
   if(trailingActivated)
   {
      double priceDifference = 0;
      bool shouldUpdate = false;
      
      if(hedgeType == "BUY")
      {
         // For BUY positions, check if price moved up enough to update SL
         priceDifference = currentPrice - lastTrailLevel;
         if(priceDifference >= (TrailingIncrement * _Point))
         {
            trailStopPrice = currentPrice - (TrailingStop * _Point);
            shouldUpdate = (trailStopPrice > currentSL); // Only update if new SL is higher
            lastTrailLevel = currentPrice;
         }
      }
      else
      {
         // For SELL positions, check if price moved down enough to update SL
         priceDifference = lastTrailLevel - currentPrice;
         if(priceDifference >= (TrailingIncrement * _Point))
         {
            trailStopPrice = currentPrice + (TrailingStop * _Point);
            shouldUpdate = (trailStopPrice < currentSL || currentSL == 0); // Only update if new SL is lower
            lastTrailLevel = currentPrice;
         }
      }
      
      // Update the trailing stop if needed
      if(shouldUpdate)
      {
         if(trade.PositionModify(hedgeTicket, trailStopPrice, 0))
         {
            Print("Updated trailing stop to ", trailStopPrice, " for ", hedgeType, " position. Price moved: ", priceDifference / _Point, " pips");
         }
         else
         {
            Print("Failed to update trailing stop. Error: ", GetLastError());
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Check if hedge position should be closed                         |
//+------------------------------------------------------------------+
void CheckHedgeManagement()
{
   // Skip if no hedge is placed
   if(!hedgePlaced) return;
   
   // Skip if we can't select the position
   if(!PositionSelectByTicket(hedgeTicket))
   {
      hedgePlaced = false;
      return;
   }
   
   // Check if we should close the hedge on recovery
   if(CloseHedgeOnRecovery)
   {
      ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
      
      // For a SHORT hedge (hedging against long positions in drawdown)
      if(posType == POSITION_TYPE_SELL && longDrawdown < DrawdownPercent / 2)
      {
         // Get the hedge profit before closing
         double hedgeProfit = PositionGetDouble(POSITION_PROFIT) + 
                              PositionGetDouble(POSITION_SWAP) +
                              PositionGetDouble(POSITION_COMMISSION);
         
         // Close the hedge
         if(trade.PositionClose(hedgeTicket))
         {
            Print("Closed SHORT hedge trade as long drawdown recovered to ", longDrawdown, "%");
            
            // Close bad positions if there's profit and it's enabled
            if(ClosePositionsAfterHedge && hedgeProfit > 0)
            {
               ClosePositionsBasedOnHedgeProfit(hedgeProfit);
            }
            
            hedgePlaced = false;
         }
      }
      // For a LONG hedge (hedging against short positions in drawdown)
      else if(posType == POSITION_TYPE_BUY && shortDrawdown < DrawdownPercent / 2)
      {
         // Get the hedge profit before closing
         double hedgeProfit = PositionGetDouble(POSITION_PROFIT) + 
                              PositionGetDouble(POSITION_SWAP) +
                              PositionGetDouble(POSITION_COMMISSION);
         
         // Close the hedge
         if(trade.PositionClose(hedgeTicket))
         {
            Print("Closed LONG hedge trade as short drawdown recovered to ", shortDrawdown, "%");
            
            // Close bad positions if there's profit and it's enabled
            if(ClosePositionsAfterHedge && hedgeProfit > 0)
            {
               ClosePositionsBasedOnHedgeProfit(hedgeProfit);
            }
            
            hedgePlaced = false;
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Create a text label on the chart                                 |
//+------------------------------------------------------------------+
void CreateLabel(string name, string text, color textColor, ENUM_BASE_CORNER corner, int x, int y)
{
   // Delete if exists
   ObjectDelete(0, name);
   
   // Create new label object
   if(!ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0))
   {
      Print("Error creating label: ", GetLastError());
      return;
   }
   
   // Set object properties
   ObjectSetInteger(0, name, OBJPROP_CORNER, corner);
   ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
   ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
   ObjectSetString(0, name, OBJPROP_TEXT, text);
   ObjectSetString(0, name, OBJPROP_FONT, "Arial Bold");
   ObjectSetInteger(0, name, OBJPROP_FONTSIZE, FontSize);
   ObjectSetInteger(0, name, OBJPROP_COLOR, textColor);
   
   // Background settings
   if(ShowBackground)
   {
      ObjectSetInteger(0, name, OBJPROP_BGCOLOR, clrBlack);
      ObjectSetInteger(0, name, OBJPROP_BACK, false);
      ObjectSetInteger(0, name, OBJPROP_BORDER_COLOR, clrWhite);
      ObjectSetInteger(0, name, OBJPROP_BORDER_TYPE, BORDER_FLAT);
   }
   
   ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
   ObjectSetInteger(0, name, OBJPROP_SELECTED, false);
   ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
   ObjectSetInteger(0, name, OBJPROP_ZORDER, 100); // Bring to front
}

//+------------------------------------------------------------------+
//| Get the profit from the hedge position that was closed           |
//+------------------------------------------------------------------+
double GetHedgeProfit(ulong ticketNumber)
{
   double profit = 0;
   bool foundProfit = false;
   
   // First check in history deals for this ticket
   for(int i = 0; i < HistoryDealsTotal(); i++)
   {
      ulong dealTicket = HistoryDealGetTicket(i);
      if(dealTicket > 0)
      {
         // Check if this deal is related to our position ticket
         if(HistoryDealGetInteger(dealTicket, DEAL_POSITION_ID) == ticketNumber)
         {
            // Get the profit from this deal
            double dealProfit = HistoryDealGetDouble(dealTicket, DEAL_PROFIT);
            double dealSwap = HistoryDealGetDouble(dealTicket, DEAL_SWAP);
            double dealCommission = HistoryDealGetDouble(dealTicket, DEAL_COMMISSION);
            
            profit += dealProfit + dealSwap + dealCommission;
            foundProfit = true;
            
            // Debug info
            Print("Found hedge deal: Ticket=", dealTicket, 
                  ", Profit=", dealProfit, 
                  ", Swap=", dealSwap, 
                  ", Commission=", dealCommission,
                  ", Total=", dealProfit + dealSwap + dealCommission);
         }
      }
   }
   
   // If we found profit info in history, use it
   if(foundProfit)
   {
      Print("Final calculated hedge profit: ", profit);
      return profit;
   }
   
   // If history deals didn't work, use a safer method
   Print("Warning: Could not find hedge position in history, using conservative 0 profit estimate");
   return 0;
}

//+------------------------------------------------------------------+
//| Close positions based on hedge profit                            |
//+------------------------------------------------------------------+
void ClosePositionsBasedOnHedgeProfit(double hedgeProfit)
{
   // Create a log file for better debugging
   int fileHandle = FileOpen("DrawdownHedger_log.txt", FILE_WRITE|FILE_TXT);
   if(fileHandle != INVALID_HANDLE)
   {
      FileWrite(fileHandle, "--- HEDGE CLOSURE LOG ---");
      FileWrite(fileHandle, "Hedge profit: ", hedgeProfit);
   }
   
   // Double check profit is actually positive
   if(hedgeProfit <= 0.0)
   {
      Print("Hedge position closed with loss or zero profit (", hedgeProfit, "), skipping position closure");
      if(fileHandle != INVALID_HANDLE)
      {
         FileWrite(fileHandle, "Hedge closed with loss/zero profit, skipping closure");
         FileClose(fileHandle);
      }
      return;
   }
   
   // Safety check - don't proceed with unreasonably large profit values
   if(hedgeProfit > 1000.0)
   {
      Print("Warning: Unusually large hedge profit detected (", hedgeProfit, "), capping at 1000.0");
      if(fileHandle != INVALID_HANDLE)
         FileWrite(fileHandle, "Capped large hedge profit at 1000.0");
      hedgeProfit = 1000.0;
   }
   
   // Convert to cents for easier calculation
   int hedgeProfitCents = (int)(hedgeProfit * 100); // Convert to cents
   int closedLossCents = 0; // Running total of closed position losses in cents
   
   Print("Looking to close positions up to ", hedgeProfitCents, " cents");
   if(fileHandle != INVALID_HANDLE)
      FileWrite(fileHandle, "Looking to close positions up to ", hedgeProfitCents, " cents");
   
   // Get all positions with their drawdown - THIS TIME CHECK ALL POSITIONS
   PositionInfo positions[];
   int positionCount = GetAllPositions(positions);
   
   if(positionCount == 0)
   {
      Print("No positions found to close");
      if(fileHandle != INVALID_HANDLE)
      {
         FileWrite(fileHandle, "No positions found to close");
         FileClose(fileHandle);
      }
      return;
   }
   
   Print("Found ", positionCount, " total positions to evaluate for closure");
   if(fileHandle != INVALID_HANDLE)
      FileWrite(fileHandle, "Found ", positionCount, " total positions to evaluate for closure");
   
   // Custom sort positions by drawdown (highest first)
   SortPositionsByDrawdown(positions, positionCount);
   
   // Flag to indicate if we've closed at least one position
   bool closedAtLeastOne = false;
   
   // First, log all positions and their losses to aid debugging
   for(int i = 0; i < positionCount; i++)
   {
      string posInfo = StringFormat("Position %d: Ticket=%d, Drawdown=%.2f, Profit=%.2f, Loss in cents=%d, Symbol=%s",
                                    i+1, positions[i].ticket, positions[i].drawdownValue, positions[i].profit,
                                    (positions[i].profit < 0) ? (int)(MathAbs(positions[i].profit) * 100) : 0,
                                    PositionGetSymbol(positions[i].ticket));
      
      Print(posInfo);
      if(fileHandle != INVALID_HANDLE)
         FileWrite(fileHandle, posInfo);
   }
   
   // SIMPLIFIED APPROACH: Find the first losing position that fits within our hedge profit
   bool forcedAtLeastOne = false;
   
   // Close positions starting from highest drawdown until we reach hedge profit
   for(int i = 0; i < positionCount; i++)
   {
      // Skip if this is a hedge position
      if(positions[i].magicNumber == HedgeMagic) 
      {
         string hedgeMsg = StringFormat("Skipping position %d (is a hedge position)", positions[i].ticket);
         Print(hedgeMsg);
         if(fileHandle != INVALID_HANDLE)
            FileWrite(fileHandle, hedgeMsg);
         continue;
      }
      
      // Try to close any position that's losing money
      if(positions[i].profit < 0)
      {
         int positionLossCents = (int)(MathAbs(positions[i].profit) * 100);
         string lossMsg = StringFormat("Position %d has loss of %d cents", positions[i].ticket, positionLossCents);
         Print(lossMsg);
         if(fileHandle != INVALID_HANDLE)
            FileWrite(fileHandle, lossMsg);
         
         // AGGRESSIVE APPROACH: Close positions even if they exceed our budget slightly
         // but make sure we close at least one position no matter what
         bool shouldClose = false;
         
         if(!closedAtLeastOne && !forcedAtLeastOne)
         {
            // Force close at least one position with highest drawdown
            forcedAtLeastOne = true;
            shouldClose = true;
            string forceMsg = "FORCING closure of highest drawdown position regardless of size";
            Print(forceMsg);
            if(fileHandle != INVALID_HANDLE)
               FileWrite(fileHandle, forceMsg);
         }
         else if(closedLossCents + positionLossCents <= hedgeProfitCents * 1.2) // Allow 20% overage
         {
            // This position's loss fits within our remaining budget (with some flexibility)
            shouldClose = true;
            string fitMsg = StringFormat("Position loss (%d cents) fits within budget (%d/%d cents)", 
                       positionLossCents, closedLossCents, hedgeProfitCents);
            Print(fitMsg);
            if(fileHandle != INVALID_HANDLE)
               FileWrite(fileHandle, fitMsg);
         }
         
         if(shouldClose)
         {
            // Close this position
            string attemptMsg = StringFormat("Attempting to close position %d", positions[i].ticket);
            Print(attemptMsg);
            if(fileHandle != INVALID_HANDLE)
               FileWrite(fileHandle, attemptMsg);
            
            if(trade.PositionClose(positions[i].ticket))
            {
               closedLossCents += positionLossCents;
               closedAtLeastOne = true;
               string successMsg = StringFormat("Successfully closed position %d with loss of %d cents. Running total: %d/%d cents", 
                            positions[i].ticket, positionLossCents, closedLossCents, hedgeProfitCents);
               Print(successMsg);
               if(fileHandle != INVALID_HANDLE)
                  FileWrite(fileHandle, successMsg);
            }
            else
            {
               int errorCode = GetLastError();
               string errorMsg = StringFormat("Failed to close position %d. Error: %d", 
                            positions[i].ticket, errorCode);
               Print(errorMsg);
               if(fileHandle != INVALID_HANDLE)
                  FileWrite(fileHandle, errorMsg);
            }
         }
         else
         {
            string skipMsg = StringFormat("Position %d exceeds remaining budget (%d+%d > %d)", 
                        positions[i].ticket, closedLossCents, positionLossCents, hedgeProfitCents);
            Print(skipMsg);
            if(fileHandle != INVALID_HANDLE)
               FileWrite(fileHandle, skipMsg);
         }
      }
      else
      {
         string profitMsg = StringFormat("Skipping position %d (is profitable with %.2f)", 
                      positions[i].ticket, positions[i].profit);
         Print(profitMsg);
         if(fileHandle != INVALID_HANDLE)
            FileWrite(fileHandle, profitMsg);
      }
      
      // Stop if we've used up our hedge profit with some flexibility
      if(closedLossCents >= hedgeProfitCents * 1.1)
      {
         string limitMsg = "Reached hedge profit limit. Stopping closure.";
         Print(limitMsg);
         if(fileHandle != INVALID_HANDLE)
            FileWrite(fileHandle, limitMsg);
         break;
      }
   }
   
   if(!closedAtLeastOne && positionCount > 0)
   {
      string warningMsg = StringFormat("WARNING: Did not close any positions despite having %d positions! This is unusual.", positionCount);
      Print(warningMsg);
      if(fileHandle != INVALID_HANDLE)
         FileWrite(fileHandle, warningMsg);
   }
   
   string summaryMsg = StringFormat("Finished closing positions. Closed %d cents worth of drawdown positions", closedLossCents);
   Print(summaryMsg);
   if(fileHandle != INVALID_HANDLE)
   {
      FileWrite(fileHandle, summaryMsg);
      FileClose(fileHandle);
   }
}

//+------------------------------------------------------------------+
//| Get ALL positions including their drawdown values                 |
//+------------------------------------------------------------------+
int GetAllPositions(PositionInfo &positions[])
{
   // First, count how many positions exist
   int totalPositions = PositionsTotal();
   
   // Resize array for all positions
   ArrayResize(positions, totalPositions);
   
   // Now populate the array
   int posIndex = 0;
   
   for(int i = 0; i < totalPositions; i++)
   {
      ulong ticket = PositionGetTicket(i);
      if(ticket > 0)
      {
         string posSymbol = PositionGetString(POSITION_SYMBOL);
         long posMagic = PositionGetInteger(POSITION_MAGIC);
         
         // Get position details
         long positionType = PositionGetInteger(POSITION_TYPE);
         double positionVolume = PositionGetDouble(POSITION_VOLUME);
         double positionProfit = PositionGetDouble(POSITION_PROFIT);
         double positionSwap = PositionGetDouble(POSITION_SWAP);
         double positionCommission = PositionGetDouble(POSITION_COMMISSION);
         double positionInitialPrice = PositionGetDouble(POSITION_PRICE_OPEN);
         double positionCurrentPrice = PositionGetDouble(POSITION_PRICE_CURRENT);
         double totalPnL = positionProfit + positionSwap + positionCommission;
         
         // Calculate position initial value
         double contractSize = SymbolInfoDouble(posSymbol, SYMBOL_TRADE_CONTRACT_SIZE);
         double initialValue = positionVolume * contractSize * positionInitialPrice;
         
         // Calculate drawdown percentage
         double drawdownPercent = 0;
         if(initialValue > 0 && totalPnL < 0)
         {
            drawdownPercent = -totalPnL / initialValue * 100.0;
         }
         
         // Fill position info
         positions[posIndex].ticket = ticket;
         positions[posIndex].drawdownValue = MathAbs(totalPnL);
         positions[posIndex].drawdownPercent = drawdownPercent;
         positions[posIndex].volume = positionVolume;
         positions[posIndex].profit = totalPnL;
         positions[posIndex].initialValue = initialValue;
         positions[posIndex].magicNumber = posMagic;
         
         posIndex++;
      }
   }
   
   return posIndex; // This may be less than totalPositions if some tickets couldn't be selected
}

//+------------------------------------------------------------------+
//| Custom sort positions by drawdown (highest first)                |
//+------------------------------------------------------------------+
void SortPositionsByDrawdown(PositionInfo &positions[], int positionCount)
{
   for(int i = 0; i < positionCount - 1; i++)
   {
      for(int j = i + 1; j < positionCount; j++)
      {
         if(positions[i].drawdownValue < positions[j].drawdownValue)
         {
            PositionInfo temp = positions[i];
            positions[i] = positions[j];
            positions[j] = temp;
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Normalize volume according to symbol's requirements              |
//+------------------------------------------------------------------+
double NormalizeVolume(string symbol, double volume)
{
   // Get the minimum and maximum allowed volumes for the symbol
   double minVolume = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MIN);
   double maxVolume = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MAX);
   double stepVolume = SymbolInfoDouble(symbol, SYMBOL_VOLUME_STEP);
   
   // Ensure volume is not less than the minimum
   if(volume < minVolume)
      volume = minVolume;
      
   // Ensure volume is not more than the maximum
   if(volume > maxVolume)
      volume = maxVolume;
      
   // Round the volume to the nearest step
   volume = MathRound(volume / stepVolume) * stepVolume;
   
   // Ensure exact precision due to floating point math
   int digits = (int)(-MathLog10(stepVolume));
   if(digits < 0) digits = 0;
   volume = NormalizeDouble(volume, digits);
   
   return volume;
} 
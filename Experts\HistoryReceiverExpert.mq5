//+------------------------------------------------------------------+
//|                                          SimpleVolumesExpert.mq5 |
//|                                      Copyright 2024, <PERSON><PERSON> |
//|                            https://www.mql5.com/en/users/antekov |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, <PERSON><PERSON>"
#property link      "https://www.mql5.com/en/articles/15330"
#property description "The EA opens a market or pending order when"
#property description "the candle tick volume exceeds the average volume in the direction of the current candle."
#property description "If orders have not yet turned into positions, they are deleted at expiration time."
#property description "Open positions are closed only by SL or TP."

#define __VERSION__ "1.00"
#property version __VERSION__

#include "../Include/MultiCurrency/VirtualFactory.mqh"
#include "ExpertHistory.mqh"

//+------------------------------------------------------------------+
//| Input parameters                                                 |
//+------------------------------------------------------------------+
input group "::: Testing the deal history"
input string historyFileName_    = "SimpleVolumesExpert.1.19 [2021.01.01 - 2022.12.30] [10000, 34518, 1294, 3.75].history.csv";    // File with history

input group "::: Money management"
sinput double expectedDrawdown_  = 10;    // - Maximum risk (%)
sinput double fixedBalance_      = 10000; // - Used deposit (0 - use all) in the account currency
input  double scale_             = 1.00;  // - Group scaling multiplier

input group "::: Risk manager"
input bool        rmIsActive_                = false;     // - Active?
input double      rmStartBaseBalance_        = 10000;    // - Base balance
input ENUM_RM_CALC_DAILY_LOSS
rmCalcDailyLossLimit_                        = RM_CALC_DAILY_LOSS_MONEY_BB;      // - Method of calculating the daily loss
input double      rmMaxDailyLossLimit_       = 500;                              // - Daily loss
input double      rmCloseDailyPart_          = 1.0;                              // - Threshold part of the daily loss
input ENUM_RM_CALC_OVERALL_LOSS
rmCalcOverallLossLimit_                      = RM_CALC_OVERALL_LOSS_MONEY_BB;    // - Method of calculating the daily loss
input double      rmMaxOverallLossLimit_     = 1000;                             // - Overall loss
input double      rmCloseOverallPart_        = 1.0;                              // - Threshold part of the overall loss
input ENUM_RM_CALC_OVERALL_PROFIT
rmCalcOverallProfitLimit_                    = RM_CALC_OVERALL_PROFIT_MONEY_BB;  // - Method for calculating total profit
input double      rmMaxOverallProfitLimit_   = 1000000;                          // - Overall profit
input datetime    rmMaxOverallProfitDate_    = 0;                                // - Maximum time of waiting for the total profit (days)

input double      rmMaxRestoreTime_           = 0;                                // - Waiting time for the best entry on a drawdown
input double      rmLastVirtualProfitFactor_  = 1;                                // - Initial best drawdown multiplier

input group "::: Other parameters"
ulong    magic_                  = 27183;    // - Magic
input bool     useOnlyNewBars_   = true;     // - Work only at bar opening
input bool     usePrevState_     = true;     // - Load the previous state

input string   symbolsReplace_   = "";       // - Symbol replacement rules


datetime fromDate = TimeCurrent();


CVirtualAdvisor     *expert;             // EA object

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit() {
// Set parameters in the money management class
   CMoney::DepoPart(expectedDrawdown_ / 10.0);
   CMoney::FixedBalance(fixedBalance_);

// Prepare the initialization string for an EA with a group of several strategies
   string expertParams = StringFormat(
                            "class CVirtualAdvisor(\n"
                            "    class CVirtualStrategyGroup(\n"
                            "       [\n"
                            "        class CHistoryStrategy(\"%s\")\n"
                            "       ],%f\n"
                            "    ),\n"
                            "    class CVirtualRiskManager(\n"
                            "       %d,%.2f,%d,%.2f,%.2f,%d,%.2f,%.2f,%d,%.2f,%d,%.2f,%.2f"
                            "    )\n"
                            "    ,%d,%s,%d\n"
                            ")",
                            historyFileName_, scale_,
                            rmIsActive_, rmStartBaseBalance_,
                            rmCalcDailyLossLimit_, rmMaxDailyLossLimit_, rmCloseDailyPart_,
                            rmCalcOverallLossLimit_, rmMaxOverallLossLimit_, rmCloseOverallPart_,
                            rmCalcOverallProfitLimit_, rmMaxOverallProfitLimit_, rmMaxOverallProfitDate_,
                            rmMaxRestoreTime_, rmLastVirtualProfitFactor_,
                            magic_, "HistoryReceiver", useOnlyNewBars_
                         );

   PrintFormat(__FUNCTION__" | Expert Params:\n%s", expertParams);

// Create an EA handling virtual positions
   expert = NEW(expertParams);

// If the EA is not created, then return an error
   if(!expert) return INIT_FAILED;

// If an error occurred while replacing symbols, then return an error
   if(!expert.SymbolsReplace(symbolsReplace_)) return INIT_FAILED;


// If we need to restore the state,
   if(usePrevState_) {
      // Load the previous state if available
      if(!expert.Load()) return INIT_FAILED;
      expert.Tick();
   }

// Successful initialization
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick() {
   expert.Tick();
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
   if(!!expert) delete expert;
}

//+------------------------------------------------------------------+
//| Test results                                                     |
//+------------------------------------------------------------------+
double OnTester(void) {
   CExpertHistory::Export();
   return expert.Tester();
}

//+------------------------------------------------------------------+
//| Initialization before optimization                               |
//+------------------------------------------------------------------+
int OnTesterInit(void) {
   return CVirtualAdvisor::TesterInit(0);
}

//+------------------------------------------------------------------+
//| Actions after optimization pass                                  |
//+------------------------------------------------------------------+
void OnTesterPass() {
   CVirtualAdvisor::TesterPass();
}

//+------------------------------------------------------------------+
//| Actions after optimization                                       |
//+------------------------------------------------------------------+
void OnTesterDeinit(void) {
   CVirtualAdvisor::TesterDeinit();
}
//+------------------------------------------------------------------+

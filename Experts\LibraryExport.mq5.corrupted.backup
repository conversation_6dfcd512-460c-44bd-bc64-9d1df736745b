//+------------------------------------------------------------------+
//|                                                LibraryExport.mq5 |
//|                                      Copyright 2024, <PERSON><PERSON> |
//|                            https://www.mql5.com/en/users/antekov |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, <PERSON><PERSON>"
#property link      "https://www.mql5.com/en/articles/15360"
#property description "EA saves initialization strings of selected passes"
#property description "to a file with a fixed name specified in the LIBRARY_FILENAME constant."

#property version "1.01"

#include "MultiCurrency/VirtualFactory.mqh"
#include "MultiCurrency/GroupsLibrary.mqh"

//+------------------------------------------------------------------+
//| Input parameters                                                 |
//+------------------------------------------------------------------+
input group "::: Exporting from library"
sinput string fileName_  = "database683.sqlite";                     // - File with the main database
input string     passes_ = "802150,802151,802152,802153,802154,"
                           "802155,802156,802157,802158,802159,"
                           "802160,802161,802162,802164,802165,"
                           "802166,802167,802168,802169,802173";    // - Comma-separated IDs of the saved passes


//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit() {
// Call the group library export method
   CGroupsLibrary::Export(passes_, fileName_);

// Successful initialization
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//|                                                                  |
//+------------------------------------------------------------------+
void OnTick() {
   ExpertRemove();
}
//+------------------------------------------------------------------+

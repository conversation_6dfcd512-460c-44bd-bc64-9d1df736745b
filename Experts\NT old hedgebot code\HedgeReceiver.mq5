#property copyright "Copyright 2024"
#property link      ""
#property version   "1.00"
#property strict
#property description "Hedge Receiver EA for NinjaTrader trades"

// Input parameters
input uint      ServerPort = 8888;    // Server Port (MT5 server port)
input string    ServerIP   = "127.0.0.1"; // Server IP
input double    DefaultLot = 0.1;     // Default lot size if not specified
input int       Slippage  = 50;       // Maximum slippage in points

// Global variables
int socket = INVALID_HANDLE;  // Socket handle
uint timeout = 5000;         // Connection timeout in milliseconds
string portStr;             // String representation of port number
bool isError = false;       // Error flag

//+------------------------------------------------------------------+
//| Simple JSON parser class                                          |
//+------------------------------------------------------------------+
class JSONParser
{
private:
    string json_str;
    int    pos;
    
public:
    JSONParser(string js) { json_str = js; pos = 0; }
    
    // Skip whitespace
    void SkipWhitespace()
    {
        while(pos < StringLen(json_str))
        {
            ushort ch = StringGetCharacter(json_str, pos);
            if(ch != ' ' && ch != '\t' && ch != '\n' && ch != '\r')
                break;
            pos++;
        }
    }
    
    // Get string value between quotes
    string GetStringValue()
    {
        string result = "";
        pos++; // Skip opening quote
        
        while(pos < StringLen(json_str))
        {
            ushort ch = StringGetCharacter(json_str, pos);
            if(ch == '"')
                break;
            result += ShortToString(ch);
            pos++;
        }
        pos++; // Skip closing quote
        return result;
    }
    
    // Get numeric value
    double GetNumericValue()
    {
        string num = "";
        string validChars = "0123456789.-";
        
        while(pos < StringLen(json_str))
        {
            ushort ch = StringGetCharacter(json_str, pos);
            string chStr = ShortToString(ch);
            if(StringFind(validChars, chStr) < 0)
                break;
            num += chStr;
            pos++;
        }
        return StringToDouble(num);
    }
    
    // Parse JSON object and extract values
    bool ParseObject(string &out_symbol, string &out_type, 
                    double &out_volume, double &out_price, string &out_comment)
    {
        ushort ch = StringGetCharacter(json_str, pos);
        if(ch != '{') return false;
        pos++; // Skip opening brace
        
        while(pos < StringLen(json_str))
        {
            SkipWhitespace();
            ch = StringGetCharacter(json_str, pos);
            if(ch != '"') return false;
            
            string key = GetStringValue();
            SkipWhitespace();
            
            ch = StringGetCharacter(json_str, pos);
            if(ch != ':') return false;
            pos++; // Skip colon
            SkipWhitespace();
            
            if(key == "symbol")
                out_symbol = GetStringValue();
            else if(key == "type")
                out_type = GetStringValue();
            else if(key == "volume")
                out_volume = GetNumericValue();
            else if(key == "price")
                out_price = GetNumericValue();
            else if(key == "comment")
                out_comment = GetStringValue();
            else
            {
                // Skip unknown values
                while(pos < StringLen(json_str))
                {
                    ch = StringGetCharacter(json_str, pos);
                    if(ch == ',' || ch == '}')
                        break;
                    pos++;
                }
            }
            
            SkipWhitespace();
            ch = StringGetCharacter(json_str, pos);
            if(ch == '}') break;
            if(ch == ',') pos++;
        }
        
        return true;
    }
};

//+------------------------------------------------------------------+
//| Expert initialization function                                     |
//+------------------------------------------------------------------+
int OnInit()
{
   // Check if automated trading is allowed
   if(!TerminalInfoInteger(TERMINAL_TRADE_ALLOWED))
   {
      MessageBox("Please enable automated trading in MT5 settings!", "Error", MB_OK|MB_ICONERROR);
      return INIT_FAILED;
   }
   
   // Check if DLL imports are allowed
   if(!TerminalInfoInteger(TERMINAL_DLLS_ALLOWED))
   {
      MessageBox("Please enable DLL imports in MT5 settings!", "Error", MB_OK|MB_ICONERROR);
      return INIT_FAILED;
   }
   
   // Initialize string conversion
   portStr = IntegerToString(ServerPort);
   Print(StringFormat("Initializing HedgeReceiver EA on port %s...", portStr));
   
   // Create socket with error handling
   ResetLastError();
   socket = SocketCreate(SOCKET_DEFAULT);
   
   if(socket != INVALID_HANDLE)
   {
      // Connect to the Python bridge
      ResetLastError();
      if(!SocketConnect(socket, ServerIP, ServerPort, timeout))
      {
         int error = GetLastError();
         string errorMsg = StringFormat("Failed to connect to bridge on port %s. Error code: %d", portStr, error);
         Print(errorMsg);
         MessageBox(errorMsg, "Connection Error", MB_OK|MB_ICONERROR);
         SocketClose(socket);
         return INIT_FAILED;
      }
      
      Print(StringFormat("Connected to bridge on port %s", portStr));
      EventSetMillisecondTimer(100); // Check for new data every 100ms
   }
   else
   {
      int error = GetLastError();
      string errorMsg = StringFormat("Failed to create socket. Error code: %d", error);
      Print(errorMsg);
      MessageBox(errorMsg, "Socket Error", MB_OK|MB_ICONERROR);
      return INIT_FAILED;
   }
   
   // Check if trading is allowed for this symbol
   if(!SymbolInfoInteger(Symbol(), SYMBOL_TRADE_MODE))
   {
      MessageBox("Trading is not allowed for this symbol!", "Error", MB_OK|MB_ICONERROR);
      return INIT_FAILED;
   }
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                   |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   EventKillTimer();
   if(socket != INVALID_HANDLE)
      SocketClose(socket);
}

//+------------------------------------------------------------------+
//| Timer function                                                     |
//+------------------------------------------------------------------+
void OnTimer()
{
   if(socket == INVALID_HANDLE)
      return;

   // Buffer for incoming data
   uchar buffer[];
   ArrayResize(buffer, 1024);
   uint bytesRead;
   string received = "";
   
   // Read data if available
   bytesRead = SocketRead(socket, buffer, ArraySize(buffer), 100);
   
   if(bytesRead > 0)
   {
      received = CharArrayToString(buffer, 0, bytesRead);
      Print(StringFormat("Received message: %s", received));
      
      // Parse JSON and execute trade
      bool success = ProcessTradeMessage(received);
      
      // Send response
      string response = success ? "{\"status\":\"success\"}" : "{\"status\":\"error\",\"message\":\"Trade execution failed\"}";
      uchar responseBuffer[];
      StringToCharArray(response, responseBuffer);
      
      // Send response back
      if(!SocketSend(socket, responseBuffer, ArraySize(responseBuffer)))
      {
         Print(StringFormat("Failed to send response. Error code: %d", GetLastError()));
      }
   }
   
   // Check for connection errors and try to reconnect if needed
   int lastError = GetLastError();
   if(lastError == 4030 || lastError == 4031) // Socket connection errors
   {
      Print(StringFormat("Connection lost (Error code: %d). Attempting to reconnect...", lastError));
      SocketClose(socket);
      socket = SocketCreate(SOCKET_DEFAULT);
      
      if(socket != INVALID_HANDLE)
      {
         if(!SocketConnect(socket, ServerIP, ServerPort, timeout))
         {
            Print(StringFormat("Failed to reconnect. Error code: %d", GetLastError()));
            SocketClose(socket);
            socket = INVALID_HANDLE;
         }
         else
            Print("Reconnected successfully");
      }
   }
}

//+------------------------------------------------------------------+
//| Process trade message and execute hedge trade                      |
//+------------------------------------------------------------------+
bool ProcessTradeMessage(string message)
{
   // Parse JSON message
   JSONParser parser(message);
   string symbol = "";
   string type = "";
   double volume = 0.0;
   double price = 0.0;
   string comment = "";
   
   if(!parser.ParseObject(symbol, type, volume, price, comment))
   {
      Print(StringFormat("Failed to parse JSON message: %s", message));
      return false;
   }
   
   if(volume <= 0) volume = DefaultLot;
   
   // Prepare trade request
   MqlTradeRequest request = {};
   MqlTradeResult result = {};
   
   request.action = TRADE_ACTION_DEAL;
   request.symbol = symbol;
   request.volume = volume;
   request.type = (type == "Buy") ? ORDER_TYPE_BUY : ORDER_TYPE_SELL;
   request.price = SymbolInfoDouble(symbol, (request.type == ORDER_TYPE_BUY) ? SYMBOL_ASK : SYMBOL_BID);
   request.deviation = Slippage;
   request.magic = 12345;
   request.comment = comment;
   request.type_filling = ORDER_FILLING_IOC;
   
   // Execute trade
   bool success = OrderSend(request, result);
   
   if(!success)
   {
      Print(StringFormat("OrderSend failed with error #%d", GetLastError()));
      return false;
   }
   
   if(result.retcode == TRADE_RETCODE_DONE)
   {
      Print(StringFormat("Hedge trade executed successfully. Ticket: %d", result.order));
      return true;
   }
   
   Print(StringFormat("Trade execution failed. Return code: %d", result.retcode));
   return false;
}

//+------------------------------------------------------------------+
//| Expert tick function                                              |
//+------------------------------------------------------------------+
void OnTick()
{
   // Main trading logic is handled in OnTimer
} 
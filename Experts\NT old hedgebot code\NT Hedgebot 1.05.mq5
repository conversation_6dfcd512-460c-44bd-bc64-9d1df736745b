#property strict

// Input parameter to set the desired lot size
input double HedgeLotSize = 0.1; // Default lot size is 0.1 lots

// Name of the single CSV file to read trades from
string TradeFileName = "trades.csv";

// Keep track of how many lines have been processed so far
int LastLineCount = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   if(HedgeLotSize <= 0)
   {
      Print("Error: HedgeLotSize must be greater than zero.");
      return(INIT_FAILED);
   }

   LastLineCount = 0; // Force file reprocessing for debugging
   Print("EA initialized successfully. Watching file: ", TradeFileName);
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   CheckForNewTrades();
}

//+------------------------------------------------------------------+
//| Check for new trades function                                    |
//+------------------------------------------------------------------+
void CheckForNewTrades()
{
   // Re-open the file to ensure we get the latest content
   ResetLastError();
   int fileHandle = FileOpen(TradeFileName, FILE_READ | FILE_CSV);
   if(fileHandle == INVALID_HANDLE)
   {
      Print("File does not exist or cannot be accessed: ", TradeFileName, ". Error: ", GetLastError());
      return;
   }

   Print("File opened successfully. Checking for new trades...");

   int lineCount = 0;
   while(!FileIsEnding(fileHandle))
   {
      string line = FileReadString(fileHandle);
      if(StringLen(line) > 0) // Count only non-empty lines
      {
         lineCount++;
      }
   }

   FileClose(fileHandle);

   Print("Total lines in file: ", lineCount, ", Previously processed: ", LastLineCount);

   if(lineCount > LastLineCount)
   {
      int newLines = lineCount - LastLineCount;
      ParseAndExecuteTrades(newLines, LastLineCount);
      LastLineCount = lineCount; // Update the count only after processing
   }
   else
   {
      Print("No new trades found.");
   }
}

//+------------------------------------------------------------------+
//| Parse and execute trades function                                |
//+------------------------------------------------------------------+
void ParseAndExecuteTrades(int newLines, int skipLines)
{
   int fileHandle = FileOpen(TradeFileName, FILE_READ | FILE_CSV);
   if(fileHandle == INVALID_HANDLE)
   {
      Print("Failed to re-open file ", TradeFileName, ". Error: ", GetLastError());
      return;
   }

   Print("Processing ", newLines, " new trades...");

   for(int i = 0; i < skipLines; i++)
      FileReadString(fileHandle); // Skip processed lines

   for(int i = 0; i < newLines; i++)
   {
      string line = FileReadString(fileHandle);
      Print("Read line: ", line);

      if(StringLen(line) == 0 || StringFind(line, ",") == -1)
      {
         Print("Skipping invalid or corrupted line: ", line);
         continue;
      }

      string parts[];
      int partsCount = StringSplit(line, ',', parts);

      if(partsCount < 6)
      {
         Print("Skipping invalid line format: ", line);
         continue;
      }

      string instrument = parts[1];
      string direction  = parts[2];
      string mt5Symbol  = MapNTtoMT5Symbol(instrument);

      Print("Mapping NT8 symbol: ", instrument, " -> MT5 symbol: ", mt5Symbol);

      if(mt5Symbol == "")
      {
         string alertMsg = "Unrecognized symbol from CSV: " + instrument + ". Trade not placed.";
         Print(alertMsg);
         Alert(alertMsg);
         continue;
      }

      ENUM_ORDER_TYPE orderType = (direction == "Buy") ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;

      MqlTradeRequest request;
      MqlTradeResult  result;
      ZeroMemory(request);
      ZeroMemory(result);

      request.action       = TRADE_ACTION_DEAL;
      request.symbol       = mt5Symbol;
      request.volume       = HedgeLotSize;
      request.type         = orderType;
      request.price        = (orderType == ORDER_TYPE_BUY) 
                              ? SymbolInfoDouble(mt5Symbol, SYMBOL_ASK) 
                              : SymbolInfoDouble(mt5Symbol, SYMBOL_BID);
      request.deviation    = 10000;       // Large deviation to ensure execution
      request.magic        = 12345;
      request.comment      = "Hedge from NT";
      request.type_filling = ORDER_FILLING_RETURN;

      Print("Sending order: Symbol=", mt5Symbol, ", Direction=", EnumToString(orderType), ", Volume=", HedgeLotSize);

      if(!OrderSend(request, result))
      {
         Print("OrderSend failed for symbol ", mt5Symbol, " with error #", GetLastError());
      }
      else
      {
         if(result.retcode == TRADE_RETCODE_DONE)
         {
            Print("Hedge order placed successfully for symbol ", mt5Symbol, ", Ticket: ", result.order);
         }
         else
         {
            Print("OrderSend failed for symbol ", mt5Symbol, ", retcode: ", result.retcode);
         }
      }
   }

   FileClose(fileHandle);
}

//+------------------------------------------------------------------+
//| Map NinjaTrader symbol to MT5 symbol                             |
//+------------------------------------------------------------------+
string MapNTtoMT5Symbol(string ntSymbol)
{
   if(ntSymbol == "NQ") return "USTECH";
   if(ntSymbol == "GC") return "XAUUSD";
   if(ntSymbol == "ES") return "US500";
   return "";
}

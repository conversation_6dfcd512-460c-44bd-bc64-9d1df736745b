#property strict

// Input parameter to set the desired lot size
input double HedgeLotSize = 0.1; // Default lot size is 0.1 lots

// Name of the single CSV file to read trades from
string TradeFileName = "trades.csv";

// Keep track of how many lines have been processed so far
int LastLineCount = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // Validate the input lot size
   if(HedgeLotSize <= 0)
   {
      Print("Error: HedgeLotSize must be greater than zero.");
      return(INIT_FAILED);
   }

   // Initialize LastLineCount to zero
   LastLineCount = 0;

   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // Check for new trades on every tick
   CheckForNewTrades();
}

//+------------------------------------------------------------------+
//| Check for new trades function                                    |
//+------------------------------------------------------------------+
void CheckForNewTrades()
{
   // Try to open the CSV file
   int fileHandle = FileOpen(TradeFileName, FILE_READ|FILE_CSV);
   if(fileHandle == INVALID_HANDLE)
   {
      Print("Failed to open file ", TradeFileName, ". Error: ", GetLastError());
      return;
   }

   // Count how many lines are in the file
   int lineCount = 0;
   while(!FileIsEnding(fileHandle))
   {
      FileReadString(fileHandle);
      lineCount++;
   }

   FileClose(fileHandle);

   // If new lines have been added since the last check
   if(lineCount > LastLineCount)
   {
      int newLines = lineCount - LastLineCount;
      ParseAndExecuteTrades(newLines, LastLineCount);
      // Update the line count
      LastLineCount = lineCount;
   }
}

//+------------------------------------------------------------------+
//| Parse and execute trades function                                |
//+------------------------------------------------------------------+
void ParseAndExecuteTrades(int newLines, int skipLines)
{
   // Re-open the file for reading
   int fileHandle = FileOpen(TradeFileName, FILE_READ | FILE_CSV);
   if(fileHandle == INVALID_HANDLE)
   {
      Print("Failed to re-open file ", TradeFileName, ". Error: ", GetLastError());
      return;
   }

   // Skip the lines we've already processed
   for(int i = 0; i < skipLines; i++)
      FileReadString(fileHandle); // discard old lines

   // Read the new lines and process them
   for(int i = 0; i < newLines; i++)
   {
      string line = FileReadString(fileHandle);

      // Format in CSV: "timestamp,instrument,direction,quantity,price"
      string parts[];
      int partsCount = StringSplit(line, ',', parts);

      if(partsCount < 5)
      {
         Print("Invalid line format in ", TradeFileName, ": ", line);
         continue;
      }

      // Extract fields
      string timeStr    = parts[0];
      string instrument = parts[1];   // Raw symbol from NinjaTrader (NQ, GC, ES, etc.)
      string direction  = parts[2];   // "Buy" or "Sell"
      // We ignore parts[3] (quantity) since we use HedgeLotSize
      double price      = StringToDouble(parts[4]);

      // Map the NinjaTrader symbol to the MT5 symbol
      string mt5Symbol = MapNTtoMT5Symbol(instrument);
      if(mt5Symbol == "")
      {
         // Alert if the symbol is not recognized
         string alertMsg = "Unrecognized symbol from CSV: " + instrument + ". Trade not placed.";
         Print(alertMsg);
         Alert(alertMsg);
         continue; // Skip this trade
      }

      // Determine the opposite direction for hedging
      ENUM_ORDER_TYPE orderType = (direction == "Buy") ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;

      // Prepare the trade request
      MqlTradeRequest request;
      MqlTradeResult  result;
      ZeroMemory(request);
      ZeroMemory(result);

      request.action       = TRADE_ACTION_DEAL;
      request.symbol       = mt5Symbol;          // Use the mapped MT5 symbol
      request.volume       = HedgeLotSize; 
      request.type         = orderType;
      request.price        = (orderType == ORDER_TYPE_BUY) 
                              ? SymbolInfoDouble(mt5Symbol, SYMBOL_ASK) 
                              : SymbolInfoDouble(mt5Symbol, SYMBOL_BID);
      request.deviation    = 5;      // Slippage
      request.magic        = 12345;
      request.comment      = "Hedge from NT";
      request.type_filling = ORDER_FILLING_RETURN; // Adjust per broker if needed

      // Send the trade request
      if(!OrderSend(request, result))
      {
         Print("OrderSend failed for symbol ", mt5Symbol, " with error #", GetLastError());
      }
      else
      {
         if(result.retcode == TRADE_RETCODE_DONE)
         {
            Print("Hedge order placed successfully for symbol ", mt5Symbol, ", Ticket: ", result.order);
         }
         else
         {
            Print("OrderSend failed for symbol ", mt5Symbol, ", retcode: ", result.retcode);
         }
      }
   }

   FileClose(fileHandle);
}

//+------------------------------------------------------------------+
//| Map NinjaTrader symbol to MT5 symbol                             |
//+------------------------------------------------------------------+
string MapNTtoMT5Symbol(string ntSymbol)
{
   // Convert the NinjaTrader symbol to the corresponding MT5 symbol
   if(ntSymbol == "NQ")
      return "USTECH";
   else if(ntSymbol == "GC")
      return "XAUUSD";
   else if(ntSymbol == "ES")
      return "US500";

   // If it's an unknown symbol, return an empty string
   return "";
}

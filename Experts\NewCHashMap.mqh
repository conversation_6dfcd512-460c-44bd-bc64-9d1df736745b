//+------------------------------------------------------------------+
//|                                                  NewCHashMap.mqh |
//|                        Copyright 2023-2024, Your Name/Company    |
//|                                              yourwebsite.com     |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023-2024, Your Name/Company"
#property link      "yourwebsite.com"
#property version   "1.00"

#include <Object.mqh>
#include <Generic/HashSet.mqh> // For the underlying templated HashSet
#include <Generic/Internal/DefaultEqualityComparer.mqh>

//+------------------------------------------------------------------+
//| Class CHashMap.                                                  |
//| Represents a collection of keys (long) and values (CObject*).    |
//+------------------------------------------------------------------+
class CHashMap : public CObject
  {
private:
   // Internal storage using a templated HashSet
   // We need a way to store key-value pairs. HashSet stores individual items.
   // A common approach is to have a helper struct/class for the entry.
   class CMapEntry : public CObject
     {
   public:
      long              m_key;
      CObject          *m_value;
      bool              m_owner; // To manage object lifetime if CHashMap owns it

                       CMapEntry(long key, CObject *value, bool owner=false) : m_key(key), m_value(value), m_owner(owner) {}
                      ~CMapEntry()
                        {
                         if(m_owner && CheckPointer(m_value)!=POINTER_INVALID)
                           delete m_value;
                        }
      virtual int       Compare(const CObject *obj,const int mode) const override
                        {
                         const CMapEntry *other=dynamic_cast<const CMapEntry*>(obj);
                         if(CheckPointer(other)==POINTER_INVALID) return -1;
                         // Compare based on keys for HashSet's internal logic
                         if(m_key < other.m_key) return -1;
                         if(m_key > other.m_key) return 1;
                         return 0;
                        }
      long              Key(void) const { return m_key; }
      CObject          *Value(void) const { return m_value; }
      void              SetValue(CObject* value, bool owner)
                        {
                         if(m_owner && CheckPointer(m_value)!=POINTER_INVALID) delete m_value;
                         m_value = value;
                         m_owner = owner;
                        }
     };

   // Comparer for CMapEntry based on keys
   class CMapEntryComparer : public IEqualityComparer<CMapEntry*>
     {
   public:
      virtual bool      Equals(CMapEntry *obj1, CMapEntry *obj2) const override
                        {
                         if(CheckPointer(obj1)==POINTER_INVALID || CheckPointer(obj2)==POINTER_INVALID)
                           return CheckPointer(obj1)==POINTER_INVALID && CheckPointer(obj2)==POINTER_INVALID;
                         return obj1.Key() == obj2.Key();
                        }
      virtual int       HashCode(CMapEntry *obj) const override
                        {
                         if(CheckPointer(obj)==POINTER_INVALID) return 0;
                         // Simple hash for long key
                         return (int)(obj.Key() ^ (obj.Key() >> 32));
                        }
     };

   CHashSet<CMapEntry*> *m_set;
   bool                 m_free_objects; // If true, CHashMap deletes CObject values on Remove/Clear/Destruction

public:
                     CHashMap(void);
                    ~CHashMap(void);

   bool              Set(long nKey, CObject *pValue);
   bool              TryGetValue(long nKey, CObject*& pValue); // pValue is an out parameter
   void              SetFreeObjects(bool bFreeObjects);
   bool              GetFreeObjects(void) const;
   void              Clear(void);
   int               Size(void) const;
   bool              Remove(long nKey);
   
   // Helper to get all keys (optional, but can be useful)
   // int               GetKeys(long &keys_array[]);
  };
//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CHashMap::CHashMap(void) : m_free_objects(false)
  {
   m_set = new CHashSet<CMapEntry*>(new CMapEntryComparer());
   if(CheckPointer(m_set) == POINTER_INVALID)
     {
      printf("CHashMap: Failed to create internal HashSet");
     }
  }
//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
CHashMap::~CHashMap(void)
  {
   Clear(); // Clear will handle m_free_objects logic
   if(CheckPointer(m_set)!=POINTER_INVALID)
     {
      // The comparer was created with new, HashSet should delete it if it takes ownership,
      // or we need to delete it here if HashSet doesn't.
      // Standard MQL5 collections usually manage their own comparers if created internally or passed as owned.
      // Assuming CHashSet deletes the comparer passed to its constructor.
      delete m_set;
     }
  }
//+------------------------------------------------------------------+
//| Sets whether the CHashMap should delete CObject values           |
//+------------------------------------------------------------------+
void CHashMap::SetFreeObjects(bool bFreeObjects)
  {
   m_free_objects = bFreeObjects;
   // If we are now responsible for freeing objects, iterate and update ownership in CMapEntry
   // This is tricky if objects were added before this flag was set.
   // A simpler model is that ownership is determined at the time of Set.
   // For now, this flag will apply to future removals/clear.
   // To be fully robust, existing entries might need their 'm_owner' flag updated.
   // However, typical MQL5 CHashMap implies this flag controls deletion behavior,
   // and assumes objects added are "owned" if this flag is true.
  }
//+------------------------------------------------------------------+
//| Gets whether the CHashMap will delete CObject values             |
//+------------------------------------------------------------------+
bool CHashMap::GetFreeObjects(void) const
  {
   return m_free_objects;
  }
//+------------------------------------------------------------------+
//| Adds or updates an element with the specified key and value.     |
//+------------------------------------------------------------------+
bool CHashMap::Set(long nKey, CObject *pValue)
  {
   if(CheckPointer(m_set)==POINTER_INVALID) return false;

   CMapEntry* search_entry = new CMapEntry(nKey, NULL); // Temporary for searching
   if(CheckPointer(search_entry)==POINTER_INVALID) return false;

   CMapEntry* existing_entry = m_set.TryGet(search_entry);
   delete search_entry; // Clean up temporary search_entry

   if(CheckPointer(existing_entry) != POINTER_INVALID)
     {
      // Key exists, update value
      if(m_free_objects && CheckPointer(existing_entry.m_value) != POINTER_INVALID && existing_entry.m_value != pValue)
        {
         delete existing_entry.m_value; // Delete old value if owned
        }
      existing_entry.m_value = pValue;
      // existing_entry.m_owner = m_free_objects; // Update ownership if Set implies transfer
      // More simply, the CMapEntry's owner flag is set if m_free_objects is true at time of Set
      existing_entry.SetValue(pValue, m_free_objects);
      return true;
     }
   else
     {
      // Key does not exist, add new entry
      CMapEntry* new_entry = new CMapEntry(nKey, pValue, m_free_objects);
      if(CheckPointer(new_entry)==POINTER_INVALID) return false;
      if(!m_set.Add(new_entry))
        {
         delete new_entry; // Failed to add
         return false;
        }
      return true;
     }
  }
//+------------------------------------------------------------------+
//| Gets the value associated with the specified key.                |
//+------------------------------------------------------------------+
bool CHashMap::TryGetValue(long nKey, CObject*& pValue) // pValue is an out parameter
  {
   if(CheckPointer(m_set)==POINTER_INVALID) return false;
   
   pValue = NULL; // Initialize out parameter

   CMapEntry* search_entry = new CMapEntry(nKey, NULL); // Temporary for searching
   if(CheckPointer(search_entry)==POINTER_INVALID) return false;

   CMapEntry* found_entry = m_set.TryGet(search_entry);
   delete search_entry; // Clean up temporary search_entry

   if(CheckPointer(found_entry) != POINTER_INVALID)
     {
      pValue = found_entry.Value();
      return true;
     }
   return false;
  }
//+------------------------------------------------------------------+
//| Removes all elements from the CHashMap.                          |
//+------------------------------------------------------------------+
void CHashMap::Clear(void)
  {
   if(CheckPointer(m_set)==POINTER_INVALID) return;

   if(m_free_objects)
     {
      // Iterate and delete owned CMapEntry objects and their CObject values
      // CHashSet doesn't directly give an iterator in older MQL.
      // We might need to copy to an array first.
      CArrayObj* entries_list = new CArrayObj();
      if(CheckPointer(entries_list) != POINTER_INVALID)
        {
         if(m_set.CopyToList(entries_list) > 0)
           {
            for(int i = 0; i < entries_list.Total(); i++)
              {
               CMapEntry* entry = entries_list.At(i);
               if(CheckPointer(entry) != POINTER_INVALID)
                 {
                  // CMapEntry destructor handles its owned CObject* value
                  // HashSet owns the CMapEntry pointers themselves if it's a CHashSet<CMapEntry*>
                  // and it's configured to delete them.
                  // If CHashSet is just storing pointers, we delete them here.
                  // Assuming CHashSet<T*> with a custom comparer will delete T* if it's told to.
                  // For safety, if m_set.DeleteValues(true) is not available or clear,
                  // we delete CMapEntry* here.
                  // However, CMapEntry's destructor will handle the CObject* it wraps if m_owner is true.
                  // The CHashSet itself should delete the CMapEntry objects it stores when cleared or if it owns them.
                  // Let's assume CHashSet<CMapEntry*> with DeleteValues(true) (if available) or its own clear
                  // will delete the CMapEntry* objects. The CMapEntry destructor handles the CObject*.
                 }
              }
           }
         delete entries_list;
        }
     }
   // Whether m_free_objects is true or false, the CMapEntry objects themselves
   // are managed by the CHashSet (if it's a set of pointers it owns) or need to be deleted
   // if we copied them out.
   // The standard CHashSet.Clear() should handle deleting the CMapEntry* if it owns them.
   // If CHashSet is set to own its elements (e.g. via a constructor param or method),
   // its Clear() method will delete each CMapEntry*. The CMapEntry destructor
   // will then delete the CObject* it wraps if its m_owner flag is true (which is set if m_free_objects was true).
   m_set.Clear(); // This should trigger deletion of CMapEntry* if HashSet owns them.
                  // And CMapEntry destructor handles the CObject* if m_free_objects was set.
  }
//+------------------------------------------------------------------+
//| Gets the number of key/value pairs contained in the CHashMap.    |
//+------------------------------------------------------------------+
int CHashMap::Size(void) const
  {
   if(CheckPointer(m_set)==POINTER_INVALID) return 0;
   return m_set.Count();
  }
//+------------------------------------------------------------------+
//| Removes the element with the specified key from the CHashMap.    |
//+------------------------------------------------------------------+
bool CHashMap::Remove(long nKey)
  {
   if(CheckPointer(m_set)==POINTER_INVALID) return false;

   CMapEntry* search_entry = new CMapEntry(nKey, NULL);
   if(CheckPointer(search_entry)==POINTER_INVALID) return false;

   // CHashSet::Remove usually takes the object itself, not a search template.
   // We need to get it first, then remove.
   CMapEntry* entry_to_remove = m_set.TryGet(search_entry);
   delete search_entry; // clean up search_entry

   if(CheckPointer(entry_to_remove) != POINTER_INVALID)
     {
      bool removed = m_set.Remove(entry_to_remove);
      if(removed)
        {
         // If m_free_objects is true, CMapEntry's destructor (called by CHashSet when it deletes the entry)
         // should handle deleting the CObject value.
         // If CHashSet doesn't delete the entry_to_remove pointer itself, we must do it.
         // Assuming CHashSet<T*> with DeleteValues(true) or similar manages this.
         // If not, and if m_free_objects is true, we'd delete entry_to_remove.m_value, then entry_to_remove.
         // Let's assume CHashSet handles deletion of 'entry_to_remove' if it was configured to own its elements.
         // The CMapEntry destructor will handle the wrapped CObject if m_free_objects was true.
         // If CHashSet does NOT delete the CMapEntry* itself upon removal, we must:
         delete entry_to_remove; // This will trigger CMapEntry destructor
         return true;
        }
     }
   return false;
  }
//+------------------------------------------------------------------+
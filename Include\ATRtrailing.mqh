//+------------------------------------------------------------------+
//|                                                ATRtrailing.mqh |
//|                                                                  |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023"
#property link      ""
#property version   "1.00"
#property strict

// Input parameters for DEMA-ATR trailing stop
input int      DEMA_ATR_Period = 14;       // Period for DEMA-ATR calculation
input double   DEMA_ATR_Multiplier = 1.5;  // Multiplier for DEMA-ATR to determine trailing distance
input double   TrailingActivationPercent = 1.0; // Activate trailing at this percent of account balance in profit
input bool     UseATRTrailing = true;      // Enable DEMA-ATR trailing stop

// Buffers for DEMA ATR calculation
double AtrDEMA[], Ema1[], Ema2[];  // buffers for DEMA ATR, and intermediate EMAs

//+------------------------------------------------------------------+
//| Initialize DEMA-ATR arrays                                       |
//+------------------------------------------------------------------+
void InitDEMAATR()
{
    ArrayResize(AtrDEMA, 100);
    ArrayResize(Ema1, 100);
    ArrayResize(Ema2, 100);
    ArrayInitialize(AtrDEMA, 0);
    ArrayInitialize(Ema1, 0);
    ArrayInitialize(Ema2, 0);
}

//+------------------------------------------------------------------+
//| Calculate DEMA-ATR value for the current bar                     |
//+------------------------------------------------------------------+
double CalculateDEMAATR(int period = 0)
{
    if(period < 0) period = 0;
    
    // Get price data for calculation
    MqlRates rates[];
    int copied = CopyRates(_Symbol, PERIOD_CURRENT, 0, DEMA_ATR_Period + 1 + period, rates);
    
    if(copied < DEMA_ATR_Period + 1 + period)
    {
        Print("Error copying rates data: ", GetLastError());
        return 0.0;
    }
    
    double alpha = 2.0 / (DEMA_ATR_Period + 1);  // EMA smoothing factor for DEMA
    
    // Calculate initial ATR if needed
    if(Ema1[0] == 0)
    {
        double sumTR = 0.0;
        for(int j = 0; j < DEMA_ATR_Period; j++)
        {
            int idx = copied - 1 - j;
            double trj;
            if(j == 0)
                trj = rates[idx].high - rates[idx].low;
            else
            {
                double tr1 = rates[idx].high - rates[idx].low;
                double tr2 = MathAbs(rates[idx].high - rates[idx+1].close);
                double tr3 = MathAbs(rates[idx].low - rates[idx+1].close);
                trj = MathMax(tr1, MathMax(tr2, tr3));
            }
            sumTR += trj;
        }
        double initialATR = sumTR / DEMA_ATR_Period;
        Ema1[0] = initialATR;
        Ema2[0] = initialATR;
        AtrDEMA[0] = initialATR;
    }
    
    // Calculate current TR
    double TR_current;
    int current = copied - 1 - period;
    int prev = copied - 2 - period;
    
    if(prev < 0)
    {
        TR_current = rates[current].high - rates[current].low;
    }
    else
    {
        double tr1 = rates[current].high - rates[current].low;
        double tr2 = MathAbs(rates[current].high - rates[prev].close);
        double tr3 = MathAbs(rates[current].low - rates[prev].close);
        TR_current = MathMax(tr1, MathMax(tr2, tr3));
    }
    
    // Update EMA1, EMA2, and DEMA-ATR
    double ema1_current = Ema1[0] + alpha * (TR_current - Ema1[0]);
    double ema2_current = Ema2[0] + alpha * (ema1_current - Ema2[0]);
    double dema_atr = 2.0 * ema1_current - ema2_current;
    
    // Store values for next calculation
    Ema1[0] = ema1_current;
    Ema2[0] = ema2_current;
    AtrDEMA[0] = dema_atr;
    
    return dema_atr;
}

//+------------------------------------------------------------------+
//| Check if trailing stop should be activated                       |
//+------------------------------------------------------------------+
bool ShouldActivateTrailing(double entryPrice, double currentPrice, string orderType, double volume)
{
    if(!UseATRTrailing) return false;
    
    double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    double pointValue = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    
    // Calculate profit in account currency
    double priceDiff = (orderType == "BUY" ? currentPrice - entryPrice : entryPrice - currentPrice);
    double profitPoints = priceDiff / pointValue;
    double profitCurrency = profitPoints * (tickValue / tickSize) * volume;
    
    // Calculate profit as percentage of account balance
    double profitPercent = (profitCurrency / accountBalance) * 100.0;
    
    // Check if profit percentage exceeds activation threshold
    return (profitPercent >= TrailingActivationPercent);
}

//+------------------------------------------------------------------+
//| Calculate trailing stop level based on DEMA-ATR                  |
//+------------------------------------------------------------------+
double CalculateTrailingStop(string orderType, double currentPrice)
{
    double demaAtr = CalculateDEMAATR();
    double trailingDistance = demaAtr * DEMA_ATR_Multiplier;
    
    // Calculate trailing stop level based on order type
    if(orderType == "BUY")
        return currentPrice - trailingDistance;
    else
        return currentPrice + trailingDistance;
}

//+------------------------------------------------------------------+
//| Update trailing stop for a position                              |
//+------------------------------------------------------------------+
bool UpdateTrailingStop(ulong ticket, double entryPrice, string orderType)
{
    if(!UseATRTrailing) return false;
    
    // Get position information
    if(!PositionSelectByTicket(ticket))
    {
        Print("Error selecting position by ticket: ", GetLastError());
        return false;
    }
    
    double currentPrice = SymbolInfoDouble(_Symbol, orderType == "BUY" ? SYMBOL_BID : SYMBOL_ASK);
    double volume = PositionGetDouble(POSITION_VOLUME);
    double currentSL = PositionGetDouble(POSITION_SL);
    
    // Check if trailing should be activated
    if(!ShouldActivateTrailing(entryPrice, currentPrice, orderType, volume))
        return false;
    
    // Calculate new trailing stop level
    double newSL = CalculateTrailingStop(orderType, currentPrice);
    
    // Only update if the new stop loss is better than the current one
    if((orderType == "BUY" && (newSL > currentSL || currentSL == 0)) ||
       (orderType == "SELL" && (newSL < currentSL || currentSL == 0)))
    {
        // Create trade request to modify position
        MqlTradeRequest request = {};
        MqlTradeResult result = {};
        
        request.action = TRADE_ACTION_SLTP;
        request.position = ticket;
        request.symbol = _Symbol;
        request.sl = newSL;
        request.tp = PositionGetDouble(POSITION_TP); // Keep the same TP
        
        // Send the request
        if(OrderSend(request, result))
        {
            Print("DEMA-ATR Trailing stop updated for ticket ", ticket, ". New SL: ", newSL);
            return true;
        }
        else
        {
            Print("Error updating trailing stop: ", GetLastError());
            return false;
        }
    }
    
    return false;
}
//+------------------------------------------------------------------+
//|                                                    Optimizer.mqh |
//|                                      Copyright 2024, <PERSON><PERSON> |
//|                            https://www.mql5.com/en/users/antekov |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, <PERSON><PERSON>"
#property link      "https://www.mql5.com/en/users/antekov"
#property version   "1.00"

#include "OptimizerTask.mqh"

//+------------------------------------------------------------------+
//| Class for the project auto optimization manager                 |
//+------------------------------------------------------------------+
class COptimizer {
      // Current optimization task
      COptimizerTask m_task;

      // Get the number of tasks with a given status in the queue
      int TotalTasks(string status = "Queued");

      // Get the ID of the next optimization task from the queue
      ulong GetNextTaskId();

public:
      COptimizer(string p_pythonPath = NULL);       // Constructor
      void Process();                               // Main processing method
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
COptimizer::COptimizer(string p_pythonPath = NULL) {
      // Pass the interpreter path to the optimization task object
      m_task.PythonPath(p_pythonPath);
}

//+------------------------------------------------------------------+
//| Get the number of tasks with the specified status               |
//+------------------------------------------------------------------+
int COptimizer::TotalTasks(string status = "Queued") {
// Result
      int res = 0;

// Request to get the number of tasks with the specified status
      string query = "SELECT COUNT(*)"
                     "     FROM tasks t"
                     "               JOIN"
                     "               jobs j ON t.id_job = j.id_job"
                     "               JOIN"
                     "               stages s ON j.id_stage = s.id_stage"
                     "   WHERE t.status = ? "
                     "   ORDER BY s.id_stage, j.id_job, t.status LIMIT 1;";

// Open the database
      if(DB::Connect()) {
            // Execute the request
            int request = DatabasePrepare(DB::Id(), query, status);

            // If there is no error
            if(request != INVALID_HANDLE) {
                  // Data structure for reading a single string of a query result
                  struct Row {
                        int count;
                  } row;

                  // Read data from the first result string
                  if(DatabaseReadBind(request, row)) {
                        res = row.count;
                  } else {
                        // Report an error if necessary
                        PrintFormat(__FUNCTION__ " | ERROR: Reading row for request \n%s\nfailed with code %d",
                                    query, GetLastError());
                  }
            } else {
                  // Report an error if necessary
                  PrintFormat(__FUNCTION__ " | ERROR: Request \n%s\nfailed with code %d", query, GetLastError());
            }

            // Close the database
            DB::Close();
      }

      return res;
}

//+------------------------------------------------------------------+
//| Get the ID of the next optimization task from the queue         |
//+------------------------------------------------------------------+
ulong COptimizer::GetNextTaskId() {
// Result
      ulong res = 0;

// Request to get the next optimization task from the queue
      string query = "SELECT t.id_task "
                     "     FROM tasks t"
                     "               JOIN"
                     "               jobs j ON t.id_job = j.id_job"
                     "               JOIN"
                     "               stages s ON j.id_stage = s.id_stage"
                     "   WHERE t.status IN ('Queued', 'Processing')"
                     "   ORDER BY s.id_stage, j.id_job, t.status LIMIT 1;";

// Open the database
      if(DB::Connect()) {
            // Execute the request
            int request = DatabasePrepare(DB::Id(), query);

            // If there is no error
            if(request != INVALID_HANDLE) {
                  // Data structure for reading a single string of a query result
                  struct Row {
                        ulong id_task;
                  } row;

                  // Read data from the first result string
                  if(DatabaseReadBind(request, row)) {
                        res = row.id_task;
                  } else {
                        // Report an error if necessary
                        PrintFormat(__FUNCTION__ " | ERROR: Reading row for request \n%s\nfailed with code %d",
                                    query, GetLastError());
                  }
            } else {
                  // Report an error if necessary
                  PrintFormat(__FUNCTION__ " | ERROR: request \n%s\nfailed with code %d", query, GetLastError());
            }

            // Close the database
            DB::Close();
      }

      return res;
}

//+------------------------------------------------------------------+
//| Main handling method                                             |
//+------------------------------------------------------------------+
void COptimizer::Process() {
      PrintFormat(__FUNCTION__ " | Current Task ID = %d", m_task.Id());

      // If the EA is stopped, remove the timer and the EA itself from the chart
      if (IsStopped()) {
            EventKillTimer();
            ExpertRemove();
            return;
      }

      // If the current task is completed,
      if (m_task.IsDone()) {
            // If the current task is not empty,
            if(m_task.Id()) {
                  // Complete the current task
                  m_task.Finish();
            }

            // Get the number of tasks in the queue
            int totalTasks = TotalTasks("Processing") + TotalTasks("Queued");

            // If there are tasks, then
            if(totalTasks) {
                  // Get the ID of the next current task
                  ulong taskId = GetNextTaskId();

                  // Load the optimization task parameters from the database
                  m_task.Load(taskId);

                  // Launch the current task
                  m_task.Start();
                  
                  // Display the number of remaining tasks and the current task on the chart
                  Comment(StringFormat(
                            "Total tasks in queue: %d\n"
                            "Current Task ID: %d",
                            totalTasks, m_task.Id()));
            } else {
                  // If there are no tasks, remove the EA from the chart
                  PrintFormat(__FUNCTION__ " | Finish.", 0);
                  ExpertRemove();
            }
      }
}
//+------------------------------------------------------------------+

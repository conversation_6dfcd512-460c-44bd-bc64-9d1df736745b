//+------------------------------------------------------------------+
//|                                                     Receiver.mqh |
//|                                 Copyright 2022-2024, <PERSON><PERSON> |
//|                            https://www.mql5.com/en/users/antekov |
//+------------------------------------------------------------------+
#property copyright "Copyright 2022-2024, <PERSON><PERSON>"
#property link      "https://www.mql5.com/en/users/antekov"
#property version   "1.04"

//+------------------------------------------------------------------+
//| Base class for converting open volumes into market positions     |
//+------------------------------------------------------------------+
class CReceiver {
protected:
   static ulong      s_magic;       // Magic
   bool m_isChanged;                // Are there any changes in the composition of virtual positions?

public:
   CReceiver();
   virtual bool      Correct() = 0; // Adjustment of open volumes
   virtual void      Changed();     // Set the presence of external changes
};

ulong CReceiver::s_magic = 0;

//+------------------------------------------------------------------+
//|                                                                  |
//+------------------------------------------------------------------+
CReceiver::CReceiver() : m_isChanged(true) {}

//+------------------------------------------------------------------+
//|                                                                  |
//+------------------------------------------------------------------+
void CReceiver::Changed() {
   m_isChanged = true;
}
//+------------------------------------------------------------------+
